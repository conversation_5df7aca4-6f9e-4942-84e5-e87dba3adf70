package com.subfg.subfgapi.controller;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.subfg.domain.vo.FamilyGroupVo;
import com.subfg.domain.vo.Result;
import com.subfg.subfgapi.Serivce.UserFamilyGroupService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3/user/familyGroup")
@Tag(name = "用户家庭组管理", description = "用户个人家庭组相关接口")
public class UserFamilyGroupController {

    private final UserFamilyGroupService userFamilyGroupService;

    /**
     * 获取当前用户创建的家庭组列表
     * @return 用户创建的家庭组列表
     */
    @Operation(summary = "获取当前用户创建的家庭组列表", description = "获取当前登录用户创建的所有家庭组")
    @GetMapping("/getMyCreatedFamilyGroups")
    public Result<List<FamilyGroupVo>> getMyCreatedFamilyGroups(){
        List<FamilyGroupVo> list = userFamilyGroupService.getMyCreatedFamilyGroups();
        return Result.success(list);
    }

    /**
     * 获取当前用户加入的家庭组列表
     * @return 用户加入的家庭组列表
     */
    @Operation(summary = "获取当前用户加入的家庭组列表", description = "获取当前登录用户加入的所有家庭组（不包括自己创建的）")
    @GetMapping("/getMyJoinedFamilyGroups")
    public Result<List<FamilyGroupVo>> getMyJoinedFamilyGroups(){
        List<FamilyGroupVo> list = userFamilyGroupService.getMyJoinedFamilyGroups();
        return Result.success(list);
    }

    /**
     * 获取当前用户加入的拼团家庭组列表
     * @return 用户加入的拼团家庭组列表
     */
    @Operation(summary = "获取当前用户加入的拼团家庭组列表", description = "获取当前登录用户加入的所有拼团家庭组")
    @GetMapping("/getMyJoinedGroupBuyingFamilyGroups")
    public Result<List<FamilyGroupVo>> getMyJoinedGroupBuyingFamilyGroups(){
        List<FamilyGroupVo> list = userFamilyGroupService.getMyJoinedGroupBuyingFamilyGroups();
        return Result.success(list);
    }

}
